model Market {
  id         String   @id @default(uuid())
  location   Location @relation(fields: [locationId], references: [id])
  locationId String
  name       String   
  number     Int      @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Submission Submission[]
}

model MarketGeneralInfo {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  marketName     String
  marketCategory MarketCategory
  openingDays    MarketOpeningDays
}

model MarketWaterSupply {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  connectedToPipeline Boolean
  waterAvailability   WaterAvailability
  availableDays       WaterAvailabilityFrequency?
  storageCapacity     CleanWaterStorageCapacity
  mainWaterSource     MainWaterSource?
  distanceToSource    WaterSourceDistance
}

model MarketSanitation {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  toiletType                ToiletFacilityType
  slabConstructionMaterial  FacilitySlabConstructionMaterial
  totalToilets              Int
  genderSeparation          Boolean
  femaleToilets             Int
  maleToilets               Int
  girlsRoom                 Boolean
  disabilityAccess          Boolean
  staffToilets              Boolean
  hasToiletFullInLast2Years Boolean
  excretaManagement         ExcretaManagement?
}

model MarketHygiene {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  handwashingFacility           Boolean
  facilityType                  HandWashingFacilityType?
  handwashingMaterials          MarketHandWashingMaterial?
  handWashingfacilityNearToilet Boolean?
  toiletHandWashingFacilityType HandWashingFacilityType?
  toiletHandWashingMaterials    HandWashingMaterial?
}

model MarketSolidWasteManagement {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  wasteSeparation     Boolean
  wasteManagement     WasteManagementAfterSeparation?
  treatmentType       WasteTreatmentType?
  collectionFrequency WasteCollectionFrequency?
  collectionCost      Int?
}

model MarketLiquidWasteManagement {
  id           String     @id @default(uuid())
  submissionId String
  submission   Submission @relation(fields: [submissionId], references: [id])

  liquidWasteManagement WasteWaterManagement
}
