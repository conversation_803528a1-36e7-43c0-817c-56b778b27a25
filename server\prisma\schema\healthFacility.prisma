model HealthFacility {
    id         String   @id @default(uuid())
    location   Location @relation(fields: [locationId], references: [id])
    locationId String
    name       String   
    number     Int      @unique

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    Submission Submission[]
}

model HealthFacilityGeneralInfo {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    facilityName       String
    facilityType       HealthFacilityType
    managementType     HealthFacilityManagement
    dailyPatientVolume DailyPatientVolume
    totalStaff         Int
}

model HealthFacilityWaterSupply {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    connectedToPipeline Boolean
    waterAvailability   WaterAvailability
    availableDays       WaterAvailabilityFrequency?
    storageCapacity     CleanWaterStorageCapacity
    mainWaterSource     MainWaterSource?
    distanceToSource    WaterSourceDistance
}

model HealthFacilitySanitation {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    toiletType                ToiletFacilityType
    slabConstructionMaterial  FacilitySlabConstructionMaterial
    totalToilets              Int
    genderSeparation          Boolean
    femaleToilets             Int
    maleToilets               Int
    disabilityAccess          Boolean
    staffToilets              Boolean
    hasToiletFullInLast2Years Boolean
    excretaManagement         ExcretaManagement?
}

model HealthFacilityHygiene {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    handwashingFacility           Boolean
    facilityType                  HandWashingFacilityType?
    handwashingMaterials          HandWashingMaterial?
    handWashingfacilityNearToilet Boolean?
    toiletHandWashingFacilityType HandWashingFacilityType?
    toiletHandwashingMaterials    HandWashingMaterial?
}

model HealthFacilitySolidWasteManagement {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    wasteSeparation     Boolean
    wasteManagement     WasteManagementAfterSeparation?
    treatmentType       WasteTreatmentType?
    collectionFrequency WasteCollectionFrequency?
    collectionCost      Int?
}

model HealthFacilityLiquidWasteManagement {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    liquidWasteManagement WasteWaterManagement
}
