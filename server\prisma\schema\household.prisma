model HouseHold {
    id         String   @id @default(uuid())
    location   Location @relation(fields: [locationId], references: [id])
    locationId String
    number     Int      @unique

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    Submission Submission[]
}

model HouseHoldGeneralInfo {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    headOfHouseholdIdNumber String
    genderOfHead            Gender         @default(MALE)
    dateOfBirthOfHead       DateTime?
    educationLevelOfHead    EducationLevel
    householdSize           Int
    childrenUnder18         Int
    personsWithDisabilities Int
}

model HouseHoldWaterSupply {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    waterSource                 MainWaterSource
    waterAvailability           WaterAvailability
    availableDays               WaterAvailabilityFrequency?
    averageWaterCost            Int?
    storageCapacity             CleanWaterStorageCapacity
    distanceToSource            WaterSourceDistance
    timeToFetch                 WaterFetchingTime?
    jerryCanPrice               Int?
    unimprovedReason            UnimprovedWaterReason?
    pwsNonFunctionalityReason   PwsNonFunctionalityReason?
    pwsNonFunctionalityDuration PwsNonFunctionalityDuration?
}

model HouseHoldSanitation {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    toiletType                ToiletFacilityType
    toiletCompleteness        ToiletFacilityCompleteness
    slabConstructionMaterial  FacilitySlabConstructionMaterial
    hasToiletFullInLast2Years Boolean
    toiletShared              Boolean
    excretaManagement         ExcretaManagement?
}

model HouseHoldHygiene {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    handwashingFacility     Boolean
    handWashingFacilityType HandWashingFacilityType
    handwashingMaterials    HandWashingMaterial
}

model HouseHoldSolidWasteManagement {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    wasteSeparation     Boolean
    wasteManagement     WasteManagementAfterSeparation
    treatmentType       WasteTreatmentType
    collectionFrequency WasteCollectionFrequency
}

model HouseHoldLiquidWasteManagement {
    id           String     @id @default(uuid())
    submissionId String
    submission   Submission @relation(fields: [submissionId], references: [id])

    wasterWaterManagement WasteWaterManagement
}
